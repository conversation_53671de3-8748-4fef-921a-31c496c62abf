import axios, { AxiosError } from 'axios';
import Constants from 'expo-constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import {router} from "expo-router";
import {refreshAuthToken} from "@/api/authAPI";

// Constants for storage keys
const USER_JWT_TOKEN = "user-token";
const REFRESH_TOKEN = "refresh-token";

let baseURL = 'https://api.wullup.com/api';
let isRefreshing = false;
let failedQueue: any[] = [];

const getDevURL = () => {
    const debuggerHost = Constants.manifest2?.extra?.expoGo?.debuggerHost;
    if (debuggerHost) {
        const host = debuggerHost.split(':').shift();
        return `http://${host}:8001`;
    }
};


console.log("NODE_ENV", process.env.NODE_ENV);
console.log(getDevURL())

if (process.env.NODE_ENV === "development") {
    baseURL = getDevURL() + '/api';
}

export const api = axios.create({
    baseURL,
    headers: {
        'Content-Type': 'application/json',
    },
});

api.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
        const originalRequest = error.config;

        if (error.response?.status === 403 && originalRequest) {
            // Initialize retry count if not exists
            const retryCount = (originalRequest as any)._retryCount || 0;

            if (retryCount < 3) {
                (originalRequest as any)._retryCount = retryCount + 1;

                try {
                    const refreshToken = await SecureStore.getItemAsync(REFRESH_TOKEN);
                    const userJwtToken = await AsyncStorage.getItem(USER_JWT_TOKEN);
                    if (!refreshToken || !userJwtToken) {
                        throw new Error('No refresh token found');
                    }
                    const response = await refreshAuthToken(refreshToken, userJwtToken);
                    api.defaults.headers.common['Authorization'] = `Bearer ${response.userJwtToken}`;
                    await AsyncStorage.setItem(USER_JWT_TOKEN, response.userJwtToken);
                    await SecureStore.setItemAsync(REFRESH_TOKEN, response.userJwtRefreshToken);

                    return api.request(originalRequest);
                } catch (refreshError) {
                    console.error('Token refresh failed:', refreshError);
                }
            }

            if (retryCount >= 3) {
                // Redirect to login (you'll need to import your logout function)
                router.replace('/login');
            }
        }

        return Promise.reject(error);
    }
);